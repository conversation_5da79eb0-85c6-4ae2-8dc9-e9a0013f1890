#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存数据加载修复
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_multistock_data_manager():
    """测试多股票数据管理器的缓存加载功能"""
    print("🧪 测试多股票数据管理器的缓存加载功能")
    print("=" * 60)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        # 创建数据管理器
        data_manager = MultiStockDataManager(None)
        
        # 1. 测试获取所有缓存股票
        print("📊 步骤1: 获取所有缓存股票")
        cached_stocks = data_manager.get_all_cached_stocks()
        print(f"  发现缓存股票: {len(cached_stocks)} 只")
        
        if not cached_stocks:
            print("❌ 没有找到缓存股票，无法继续测试")
            return
        
        # 2. 测试加载缓存股票数据
        print(f"\n📊 步骤2: 加载缓存股票数据")
        start_date = "20240101"
        end_date = "20250625"
        
        print(f"  测试日期范围: {start_date} - {end_date}")
        print(f"  尝试加载前10只股票...")
        
        test_stocks = cached_stocks[:10]
        loaded_data = {}
        
        for i, stock_code in enumerate(test_stocks, 1):
            print(f"  {i}. 加载 {stock_code}...")
            
            try:
                # 测试直接加载方法
                data = data_manager._load_stock_data_from_cache_file(stock_code, start_date, end_date)
                
                if data is not None and not data.empty:
                    loaded_data[stock_code] = data
                    print(f"    ✅ 成功: {len(data)} 条数据")
                    
                    # 显示数据样本
                    if 'trade_date' in data.columns and 'close' in data.columns:
                        latest_date = data['trade_date'].iloc[-1]
                        latest_price = data['close'].iloc[-1]
                        print(f"    📈 最新: {latest_date}, 价格: {latest_price}")
                else:
                    print(f"    ❌ 失败: 数据为空")
                    
            except Exception as e:
                print(f"    ❌ 错误: {str(e)}")
        
        print(f"\n📊 加载结果统计:")
        print(f"  测试股票: {len(test_stocks)} 只")
        print(f"  成功加载: {len(loaded_data)} 只")
        print(f"  成功率: {len(loaded_data)/len(test_stocks)*100:.1f}%")
        
        # 3. 测试完整的load_cached_stocks_data方法
        print(f"\n📊 步骤3: 测试完整的load_cached_stocks_data方法")
        
        try:
            all_cached_data = data_manager.load_cached_stocks_data(start_date, end_date)
            
            print(f"  ✅ 成功加载: {len(all_cached_data)} 只股票")
            
            if all_cached_data:
                print(f"  📋 前5只股票:")
                for i, (stock_code, data) in enumerate(list(all_cached_data.items())[:5], 1):
                    print(f"    {i}. {stock_code}: {len(data)} 条数据")
            
            return all_cached_data
            
        except Exception as e:
            print(f"  ❌ 失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {}
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def test_backtest_system():
    """测试回测系统"""
    print(f"\n🚀 测试回测系统")
    print("=" * 60)
    
    try:
        # 检查回测系统类是否存在
        try:
            from 多股票回测系统 import MultiStockBacktestEngine
            print("✅ 找到 MultiStockBacktestEngine")
        except ImportError:
            print("❌ 未找到 MultiStockBacktestEngine，尝试其他类...")
            
            # 查看文件中有哪些类
            with open('多股票回测系统.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            import re
            classes = re.findall(r'class (\w+):', content)
            print(f"📋 文件中的类: {classes}")
            
            # 尝试使用数据管理器进行简单测试
            from 多股票回测系统 import MultiStockDataManager
            
            data_manager = MultiStockDataManager(None)
            cached_stocks = data_manager.get_all_cached_stocks()
            
            if cached_stocks:
                cached_data = data_manager.load_cached_stocks_data("20240101", "20250625")
                
                if cached_data:
                    print(f"✅ 数据加载成功: {len(cached_data)} 只股票")
                    print("💡 缓存数据回测的基础条件已满足")
                else:
                    print("❌ 数据加载失败")
            else:
                print("❌ 没有找到缓存股票")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始测试缓存数据加载修复")
    print("=" * 80)
    
    # 1. 测试多股票数据管理器
    cached_data = test_multistock_data_manager()
    
    # 2. 测试回测系统
    test_backtest_system()
    
    print("\n" + "=" * 80)
    print("📊 修复验证总结:")
    
    if cached_data:
        print(f"  ✅ 缓存数据加载成功: {len(cached_data)} 只股票")
        print(f"  ✅ 修复效果: 优秀")
        print(f"  💡 现在可以正常进行缓存数据回测了")
    else:
        print(f"  ❌ 缓存数据加载失败")
        print(f"  ⚠️ 需要进一步调试")
    
    print("\n💡 使用建议:")
    print("  1. 重新启动股票看图软件")
    print("  2. 在多股票回测界面选择'缓存数据回测'")
    print("  3. 设置回测参数并运行")
    print("  4. 应该能成功加载所有缓存股票数据")

if __name__ == "__main__":
    main()
