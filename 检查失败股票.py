#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查失败股票的日期范围
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_failed_stocks():
    """检查失败股票的日期范围"""
    print("🔍 检查失败股票的日期范围")
    print("=" * 50)
    
    # 失败的股票列表
    failed_stocks = ['000858.SZ', '002411.SZ', '300392.SZ', '600036.SH', '688555.SH']
    
    try:
        from 统一缓存管理器 import get_cache_manager
        
        cache_manager = get_cache_manager()
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        
        for stock_code in failed_stocks:
            print(f"\n📈 检查股票: {stock_code}")
            
            # 查找对应的文件
            found_file = None
            for filename in os.listdir(stocks_cache_dir):
                if filename.endswith('.json'):
                    # 解析股票代码
                    if filename.startswith('stock_daily_'):
                        # 旧格式
                        parts = filename.replace('stock_daily_', '').replace('.json', '').split('_')
                        if len(parts) >= 3:
                            file_stock_code = parts[2]
                        else:
                            file_stock_code = parts[0]
                    else:
                        # 新格式
                        file_stock_code = filename.replace('.json', '')
                    
                    # 确保有后缀
                    if '.' not in file_stock_code:
                        if file_stock_code.startswith('6'):
                            file_stock_code += '.SH'
                        elif file_stock_code.startswith(('0', '3')):
                            file_stock_code += '.SZ'
                    
                    if file_stock_code == stock_code:
                        found_file = filename
                        break
            
            if found_file:
                print(f"  📄 找到文件: {found_file}")
                
                # 读取文件
                file_path = os.path.join(stocks_cache_dir, found_file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # 解析数据
                if isinstance(file_data, dict) and 'data' in file_data:
                    if isinstance(file_data['data'], dict) and 'data' in file_data['data']:
                        # 旧格式：嵌套结构
                        data_list = file_data['data']['data']
                    else:
                        # 新格式：直接列表
                        data_list = file_data['data']
                elif isinstance(file_data, list):
                    data_list = file_data
                else:
                    print(f"  ❌ 数据格式异常")
                    continue
                
                if data_list and len(data_list) > 0:
                    df = pd.DataFrame(data_list)
                    
                    if 'trade_date' in df.columns:
                        # 检查原始日期范围
                        print(f"  📊 数据条数: {len(df)}")
                        print(f"  📅 原始日期类型: {type(df['trade_date'].iloc[0])}")
                        print(f"  📅 原始日期样本: {df['trade_date'].iloc[0]}")
                        
                        # 转换为datetime
                        if not pd.api.types.is_datetime64_any_dtype(df['trade_date']):
                            df['trade_date'] = pd.to_datetime(df['trade_date'], errors='coerce')
                        
                        print(f"  📅 日期范围: {df['trade_date'].min()} - {df['trade_date'].max()}")
                        
                        # 检查目标日期范围
                        start_dt = pd.to_datetime('20240101', format='%Y%m%d')
                        end_dt = pd.to_datetime('20250625', format='%Y%m%d')
                        
                        print(f"  🎯 目标范围: {start_dt} - {end_dt}")
                        
                        # 过滤数据
                        mask = (df['trade_date'] >= start_dt) & (df['trade_date'] <= end_dt)
                        filtered_df = df[mask]
                        
                        print(f"  📊 过滤后数据: {len(filtered_df)} 条")
                        
                        if len(filtered_df) == 0:
                            print(f"  ⚠️ 问题：数据日期范围与目标范围不重叠")
                            
                            # 检查数据是否太旧或太新
                            max_date = df['trade_date'].max()
                            min_date = df['trade_date'].min()
                            
                            if max_date < start_dt:
                                print(f"  📉 数据太旧：最新日期 {max_date} < 开始日期 {start_dt}")
                            elif min_date > end_dt:
                                print(f"  📈 数据太新：最早日期 {min_date} > 结束日期 {end_dt}")
                            else:
                                print(f"  🤔 其他原因导致过滤失败")
                        else:
                            print(f"  ✅ 过滤成功")
                    else:
                        print(f"  ❌ 没有trade_date列")
                else:
                    print(f"  ❌ 数据列表为空")
            else:
                print(f"  ❌ 未找到对应文件")
    
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_date_range_adjustment():
    """测试调整日期范围"""
    print(f"\n🧪 测试调整日期范围")
    print("=" * 50)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        data_manager = MultiStockDataManager(None)
        
        # 使用更宽的日期范围
        start_date = "20230101"  # 从2023年开始
        end_date = "20251231"    # 到2025年结束
        
        print(f"📅 使用更宽的日期范围: {start_date} - {end_date}")
        
        # 测试加载前5只股票
        cached_stocks = data_manager.get_all_cached_stocks()
        test_stocks = cached_stocks[:5]
        
        success_count = 0
        for i, stock_code in enumerate(test_stocks, 1):
            print(f"  {i}. 测试 {stock_code}...")
            
            data = data_manager._load_stock_data_from_cache_file(stock_code, start_date, end_date)
            
            if data is not None and not data.empty:
                success_count += 1
                print(f"    ✅ 成功: {len(data)} 条数据")
            else:
                print(f"    ❌ 失败")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_stocks)} 成功")
        
        if success_count == len(test_stocks):
            print(f"💡 建议：使用更宽的日期范围可以解决问题")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始检查失败股票")
    print("=" * 60)
    
    # 1. 检查失败股票的日期范围
    check_failed_stocks()
    
    # 2. 测试调整日期范围
    test_date_range_adjustment()
    
    print("\n" + "=" * 60)
    print("📊 检查总结:")
    print("  通过以上分析，可以确定失败原因并制定解决方案")

if __name__ == "__main__":
    main()
