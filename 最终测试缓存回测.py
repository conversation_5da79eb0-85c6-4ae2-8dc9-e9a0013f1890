#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试缓存回测功能
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cached_backtest():
    """测试缓存数据回测"""
    print("🚀 测试缓存数据回测")
    print("=" * 60)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        # 创建数据管理器
        data_manager = MultiStockDataManager(None)
        
        # 1. 获取缓存股票
        print("📊 步骤1: 获取缓存股票")
        cached_stocks = data_manager.get_all_cached_stocks()
        print(f"  发现缓存股票: {len(cached_stocks)} 只")
        
        if not cached_stocks:
            print("❌ 没有找到缓存股票")
            return False
        
        # 2. 使用更宽的日期范围测试数据加载
        print(f"\n📊 步骤2: 测试数据加载")
        start_date = "20230101"  # 从2023年开始
        end_date = "20251231"    # 到2025年结束
        
        print(f"  使用日期范围: {start_date} - {end_date}")
        
        # 加载缓存数据
        cached_data = data_manager.load_cached_stocks_data(start_date, end_date)
        
        print(f"  ✅ 成功加载: {len(cached_data)} 只股票")
        
        if not cached_data:
            print("❌ 没有成功加载任何股票数据")
            return False
        
        # 3. 显示加载成功的股票样本
        print(f"\n📊 步骤3: 数据样本")
        sample_stocks = list(cached_data.items())[:5]
        
        for i, (stock_code, data) in enumerate(sample_stocks, 1):
            print(f"  {i}. {stock_code}: {len(data)} 条数据")
            if not data.empty:
                date_range = f"{data['trade_date'].min()} - {data['trade_date'].max()}"
                latest_price = data['close'].iloc[-1]
                print(f"     日期范围: {date_range}")
                print(f"     最新价格: {latest_price}")
        
        # 4. 测试简单的回测逻辑
        print(f"\n📊 步骤4: 测试简单回测逻辑")
        
        # 计算每只股票的收益率
        returns = {}
        for stock_code, data in cached_data.items():
            if len(data) >= 2:
                start_price = data['close'].iloc[0]
                end_price = data['close'].iloc[-1]
                return_rate = (end_price - start_price) / start_price * 100
                returns[stock_code] = return_rate
        
        print(f"  计算了 {len(returns)} 只股票的收益率")
        
        # 显示前10只股票的收益率
        sorted_returns = sorted(returns.items(), key=lambda x: x[1], reverse=True)
        
        print(f"  📈 收益率前10名:")
        for i, (stock_code, return_rate) in enumerate(sorted_returns[:10], 1):
            print(f"    {i}. {stock_code}: {return_rate:.2f}%")
        
        print(f"  📉 收益率后10名:")
        for i, (stock_code, return_rate) in enumerate(sorted_returns[-10:], 1):
            print(f"    {i}. {stock_code}: {return_rate:.2f}%")
        
        # 5. 统计信息
        print(f"\n📊 步骤5: 统计信息")
        positive_returns = [r for r in returns.values() if r > 0]
        negative_returns = [r for r in returns.values() if r < 0]
        
        print(f"  总股票数: {len(returns)}")
        print(f"  盈利股票: {len(positive_returns)} 只 ({len(positive_returns)/len(returns)*100:.1f}%)")
        print(f"  亏损股票: {len(negative_returns)} 只 ({len(negative_returns)/len(returns)*100:.1f}%)")
        
        if returns:
            avg_return = sum(returns.values()) / len(returns)
            max_return = max(returns.values())
            min_return = min(returns.values())
            
            print(f"  平均收益率: {avg_return:.2f}%")
            print(f"  最高收益率: {max_return:.2f}%")
            print(f"  最低收益率: {min_return:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_backtest_system_integration():
    """测试回测系统集成"""
    print(f"\n🔧 测试回测系统集成")
    print("=" * 60)
    
    try:
        # 检查是否可以导入回测相关类
        try:
            from 多股票回测系统 import MultiStockBacktestEngine
            print("✅ 成功导入 MultiStockBacktestEngine")
            
            # 尝试创建回测引擎
            backtest_engine = MultiStockBacktestEngine()
            print("✅ 成功创建回测引擎")
            
        except Exception as e:
            print(f"❌ 导入回测引擎失败: {str(e)}")
            return False
        
        # 检查策略
        try:
            from 回测引擎 import MACDStrategy
            strategy = MACDStrategy()
            print("✅ 成功创建MACD策略")
            
        except Exception as e:
            print(f"❌ 导入策略失败: {str(e)}")
            return False
        
        print("✅ 回测系统集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始最终测试缓存回测功能")
    print("=" * 80)
    
    # 1. 测试缓存数据回测
    data_test_success = test_cached_backtest()
    
    # 2. 测试回测系统集成
    system_test_success = test_backtest_system_integration()
    
    print("\n" + "=" * 80)
    print("📊 最终测试总结:")
    
    if data_test_success and system_test_success:
        print("  ✅ 缓存数据回测功能完全正常")
        print("  🎉 修复成功！")
        
        print("\n💡 使用指南:")
        print("  1. 重新启动股票看图软件")
        print("  2. 在多股票回测界面选择'缓存数据回测'")
        print("  3. 设置回测参数:")
        print("     - 开始日期: 2023-01-01")
        print("     - 结束日期: 2025-12-31")
        print("     - 选择合适的策略")
        print("  4. 点击开始回测")
        print("  5. 等待回测完成并查看结果")
        
    elif data_test_success:
        print("  ✅ 缓存数据加载正常")
        print("  ⚠️ 回测系统集成有问题")
        print("  💡 数据层面的修复已完成，可以进行缓存数据回测")
        
    else:
        print("  ❌ 缓存数据回测仍有问题")
        print("  ⚠️ 需要进一步调试")
    
    print(f"\n📈 关键改进:")
    print(f"  1. ✅ 修复了文件名解析逻辑，支持新旧两种格式")
    print(f"  2. ✅ 修复了JSON数据解析，支持嵌套和直接格式")
    print(f"  3. ✅ 修复了日期格式处理，智能识别datetime格式")
    print(f"  4. ✅ 添加了缓存文件验证，确保只返回有效股票")
    print(f"  5. ✅ 改进了错误处理和调试信息")

if __name__ == "__main__":
    main()
