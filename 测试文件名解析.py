#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件名解析功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def extract_stock_code_from_filename(filename):
    """从文件名中提取股票代码（支持新旧两种格式）"""
    try:
        # 移除.json后缀
        name_without_ext = filename.replace('.json', '')
        
        # 新格式：直接是股票代码（如 000001.SZ）
        if not name_without_ext.startswith('stock_daily_'):
            stock_code = name_without_ext
        else:
            # 旧格式：stock_daily_{股票代码}_end_date_{日期}_start_date_{日期}
            # 例如：stock_daily_000001.SZ_end_date_20240112_start_date_20240101
            parts = name_without_ext.split('_')
            if len(parts) >= 3:
                stock_code = parts[2]  # 第3个部分是股票代码
            else:
                stock_code = name_without_ext
        
        # 确保股票代码格式正确
        if '.' not in stock_code:
            if stock_code.startswith('6'):
                stock_code += '.SH'
            elif stock_code.startswith(('0', '3')):
                stock_code += '.SZ'
            elif stock_code.startswith('8'):
                stock_code += '.BJ'  # 北交所
            elif stock_code.startswith('4'):
                stock_code += '.SZ'  # 新三板转板
        
        return stock_code
        
    except Exception as e:
        print(f"⚠️ 解析文件名失败 {filename}: {str(e)}")
        return None

def test_get_all_cached_stocks():
    """测试获取所有缓存股票"""
    print("🧪 测试获取所有缓存股票")
    print("=" * 50)
    
    try:
        from 统一缓存管理器 import get_cache_manager
        
        cache_manager = get_cache_manager()
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return []
        
        cached_stocks = []
        
        for filename in os.listdir(stocks_cache_dir):
            if filename.endswith('.json'):
                try:
                    # 使用统一的文件名解析逻辑（支持新旧两种格式）
                    stock_code = extract_stock_code_from_filename(filename)
                    
                    if stock_code and stock_code not in cached_stocks:
                        cached_stocks.append(stock_code)
                except Exception as e:
                    print(f"⚠️ 处理文件 {filename} 失败: {str(e)}")
                    continue
        
        print(f"📊 发现缓存股票: {len(cached_stocks)} 只")
        
        if cached_stocks:
            print(f"📋 前10只股票:")
            for i, stock in enumerate(cached_stocks[:10], 1):
                print(f"  {i}. {stock}")
        
        return cached_stocks
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_multistock_data_manager():
    """测试多股票数据管理器"""
    print(f"\n🔄 测试多股票数据管理器")
    print("=" * 50)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        # 创建数据管理器
        data_manager = MultiStockDataManager(None)
        
        # 获取所有缓存股票
        cached_stocks = data_manager.get_all_cached_stocks()
        
        print(f"📊 MultiStockDataManager发现缓存股票: {len(cached_stocks)} 只")
        
        if cached_stocks:
            print(f"📋 前10只股票:")
            for i, stock in enumerate(cached_stocks[:10], 1):
                print(f"  {i}. {stock}")
        else:
            print("❌ MultiStockDataManager没有找到任何缓存股票")
            
            # 调试：检查缓存目录
            print("\n🔍 调试信息:")
            print(f"  缓存管理器类型: {type(data_manager.batch_loader.cache_manager)}")
            
            cache_manager = data_manager.batch_loader.cache_manager
            stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
            print(f"  缓存目录: {stocks_cache_dir}")
            print(f"  目录存在: {os.path.exists(stocks_cache_dir)}")
            
            if os.path.exists(stocks_cache_dir):
                json_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
                print(f"  JSON文件数量: {len(json_files)}")
                
                if json_files:
                    print(f"  前5个文件:")
                    for i, filename in enumerate(json_files[:5], 1):
                        stock_code = data_manager._extract_stock_code_from_filename(filename)
                        print(f"    {i}. {filename} -> {stock_code}")
        
        return cached_stocks
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("🚀 开始测试文件名解析功能")
    print("=" * 60)
    
    # 1. 直接测试文件名解析
    cached_stocks1 = test_get_all_cached_stocks()
    
    # 2. 测试多股票数据管理器
    cached_stocks2 = test_multistock_data_manager()
    
    print("\n" + "=" * 60)
    print("📊 测试结果对比:")
    print(f"  直接解析: {len(cached_stocks1)} 只股票")
    print(f"  数据管理器: {len(cached_stocks2)} 只股票")
    
    if len(cached_stocks1) > 0 and len(cached_stocks2) == 0:
        print("  ⚠️ 问题：直接解析成功，但数据管理器失败")
        print("  💡 可能原因：数据管理器中的方法有问题")
    elif len(cached_stocks1) == 0:
        print("  ⚠️ 问题：文件名解析本身有问题")
    elif len(cached_stocks1) == len(cached_stocks2):
        print("  ✅ 文件名解析功能正常")

if __name__ == "__main__":
    main()
