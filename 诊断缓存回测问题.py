#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断缓存数据回测失败问题
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_get_all_cached_stocks():
    """测试获取所有缓存股票"""
    print("🧪 测试获取所有缓存股票")
    print("=" * 50)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        # 创建数据管理器
        data_manager = MultiStockDataManager(None)
        
        # 获取所有缓存股票
        cached_stocks = data_manager.get_all_cached_stocks()
        
        print(f"📊 发现缓存股票: {len(cached_stocks)} 只")
        
        if cached_stocks:
            print(f"📋 前10只股票:")
            for i, stock in enumerate(cached_stocks[:10], 1):
                print(f"  {i}. {stock}")
            
            if len(cached_stocks) > 10:
                print(f"  ... 还有 {len(cached_stocks) - 10} 只股票")
        else:
            print("❌ 没有找到任何缓存股票")
        
        return cached_stocks
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def test_load_cached_stocks_data(cached_stocks):
    """测试加载缓存股票数据"""
    print(f"\n🔄 测试加载缓存股票数据")
    print("=" * 50)
    
    if not cached_stocks:
        print("❌ 没有缓存股票可测试")
        return {}
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        # 创建数据管理器
        data_manager = MultiStockDataManager(None)
        
        # 设置测试日期范围
        start_date = "20240101"
        end_date = "20250625"
        
        print(f"📅 测试日期范围: {start_date} - {end_date}")
        print(f"📊 尝试加载 {len(cached_stocks)} 只股票")
        
        # 加载缓存数据
        cached_data = data_manager.load_cached_stocks_data(start_date, end_date)
        
        print(f"✅ 成功加载: {len(cached_data)} 只股票")
        
        if cached_data:
            print(f"\n📋 加载成功的股票:")
            for i, (stock_code, data) in enumerate(cached_data.items(), 1):
                if i <= 10:  # 只显示前10个
                    print(f"  {i}. {stock_code}: {len(data)} 条数据")
                    if not data.empty:
                        latest_date = data['trade_date'].max()
                        latest_price = data[data['trade_date'] == latest_date]['close'].iloc[0]
                        print(f"     最新日期: {latest_date}, 最新价格: {latest_price}")
            
            if len(cached_data) > 10:
                print(f"  ... 还有 {len(cached_data) - 10} 只股票")
        
        return cached_data
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def test_unified_cache_manager():
    """测试统一缓存管理器"""
    print(f"\n🔧 测试统一缓存管理器")
    print("=" * 50)
    
    try:
        from 统一缓存管理器 import get_cache_manager
        
        cache_manager = get_cache_manager()
        
        # 获取缓存目录
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return
        
        # 获取所有JSON文件
        json_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
        print(f"📊 发现 {len(json_files)} 个JSON文件")
        
        # 分析文件格式
        new_format_count = 0
        old_format_count = 0
        
        for filename in json_files:
            if filename.startswith('stock_daily_'):
                old_format_count += 1
            else:
                new_format_count += 1
        
        print(f"📋 文件格式分布:")
        print(f"  新格式: {new_format_count} 个")
        print(f"  旧格式: {old_format_count} 个")
        
        # 测试加载几个文件
        test_files = json_files[:5]
        print(f"\n🧪 测试加载前5个文件:")
        
        for filename in test_files:
            try:
                # 解析股票代码
                if filename.startswith('stock_daily_'):
                    # 旧格式
                    parts = filename.replace('stock_daily_', '').replace('.json', '').split('_')
                    if len(parts) >= 3:
                        stock_code = parts[2]
                    else:
                        stock_code = parts[0]
                else:
                    # 新格式
                    stock_code = filename.replace('.json', '')
                
                # 确保有后缀
                if '.' not in stock_code:
                    if stock_code.startswith('6'):
                        stock_code += '.SH'
                    elif stock_code.startswith(('0', '3')):
                        stock_code += '.SZ'
                
                print(f"  📈 {filename} -> {stock_code}")
                
                # 尝试用统一缓存管理器加载
                data = cache_manager.load_stock_data(stock_code, "20240101", "20250625")
                
                if data is not None and not data.empty:
                    print(f"    ✅ 成功加载: {len(data)} 条数据")
                else:
                    print(f"    ❌ 加载失败或数据为空")
                    
                    # 尝试直接读取文件
                    file_path = os.path.join(stocks_cache_dir, filename)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                    
                    if isinstance(file_data, dict) and 'data' in file_data:
                        if isinstance(file_data['data'], dict) and 'data' in file_data['data']:
                            # 旧格式：嵌套结构
                            data_list = file_data['data']['data']
                        else:
                            # 新格式：直接列表
                            data_list = file_data['data']
                        
                        print(f"    📊 直接读取文件: {len(data_list)} 条数据")
                    else:
                        print(f"    ⚠️ 文件格式异常")
                
            except Exception as e:
                print(f"    ❌ 处理失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_backtest_system():
    """测试回测系统"""
    print(f"\n🚀 测试回测系统")
    print("=" * 50)
    
    try:
        from 多股票回测系统 import MultiStockBacktestSystem
        from 回测引擎 import MACDStrategy
        
        # 创建回测系统
        backtest_system = MultiStockBacktestSystem()
        
        # 设置策略
        strategy = MACDStrategy()
        backtest_system.set_strategy(strategy)
        
        # 尝试运行缓存股票回测
        print("🔄 尝试运行缓存股票回测...")
        
        start_date = "20240101"
        end_date = "20250625"
        
        try:
            results = backtest_system.run_cached_stocks_backtest(start_date, end_date)
            print(f"✅ 回测成功: {len(results)} 只股票")
        except ValueError as e:
            print(f"❌ 回测失败: {str(e)}")
            
            # 如果失败，尝试诊断原因
            print("\n🔍 诊断失败原因:")
            data_manager = backtest_system.data_manager
            cached_stocks = data_manager.get_all_cached_stocks()
            print(f"  发现缓存股票: {len(cached_stocks)} 只")
            
            if cached_stocks:
                cached_data = data_manager.load_cached_stocks_data(start_date, end_date)
                print(f"  成功加载数据: {len(cached_data)} 只")
                
                if not cached_data:
                    print("  ⚠️ 问题：虽然发现了缓存股票，但无法加载数据")
            else:
                print("  ⚠️ 问题：没有发现任何缓存股票")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始诊断缓存数据回测问题")
    print("=" * 60)
    
    # 1. 测试获取所有缓存股票
    cached_stocks = test_get_all_cached_stocks()
    
    # 2. 测试统一缓存管理器
    test_unified_cache_manager()
    
    # 3. 测试加载缓存股票数据
    cached_data = test_load_cached_stocks_data(cached_stocks)
    
    # 4. 测试回测系统
    test_backtest_system()
    
    print("\n" + "=" * 60)
    print("📊 诊断总结:")
    print(f"  发现缓存股票: {len(cached_stocks)} 只")
    print(f"  成功加载数据: {len(cached_data)} 只")
    
    if len(cached_stocks) > 0 and len(cached_data) == 0:
        print("  ⚠️ 问题：发现了缓存股票但无法加载数据")
        print("  💡 可能原因：")
        print("    1. 统一缓存管理器无法识别文件格式")
        print("    2. 文件路径或命名规则不匹配")
        print("    3. JSON数据结构不兼容")
    elif len(cached_stocks) == 0:
        print("  ⚠️ 问题：没有发现任何缓存股票")
        print("  💡 可能原因：")
        print("    1. 缓存目录路径错误")
        print("    2. 文件名解析逻辑有问题")
    elif len(cached_data) > 0:
        print("  ✅ 缓存系统工作正常")
    
    print("\n💡 建议:")
    print("  1. 检查缓存目录和文件格式")
    print("  2. 确保统一缓存管理器能正确加载所有格式的文件")
    print("  3. 验证多股票回测系统的数据加载逻辑")

if __name__ == "__main__":
    main()
