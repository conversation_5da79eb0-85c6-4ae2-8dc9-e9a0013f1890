#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试缓存文件加载问题
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_cache_file_loading():
    """调试缓存文件加载"""
    print("🔍 调试缓存文件加载")
    print("=" * 50)
    
    try:
        from 统一缓存管理器 import get_cache_manager
        
        cache_manager = get_cache_manager()
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return
        
        # 获取前5个文件进行详细调试
        json_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
        test_files = json_files[:5]
        
        print(f"📊 测试文件: {len(test_files)} 个")
        
        for i, filename in enumerate(test_files, 1):
            print(f"\n🔍 调试文件 {i}: {filename}")
            
            try:
                # 1. 解析股票代码
                stock_code = extract_stock_code_from_filename(filename)
                print(f"  📈 解析股票代码: {stock_code}")
                
                # 2. 读取文件内容
                file_path = os.path.join(stocks_cache_dir, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                print(f"  📄 文件数据类型: {type(file_data)}")
                
                if isinstance(file_data, dict):
                    print(f"  🔑 字典键: {list(file_data.keys())}")
                    
                    if 'data' in file_data:
                        data_content = file_data['data']
                        print(f"  📊 data内容类型: {type(data_content)}")
                        
                        if isinstance(data_content, dict):
                            print(f"  🔑 data字典键: {list(data_content.keys())}")
                            
                            if 'data' in data_content:
                                # 旧格式：嵌套结构
                                data_list = data_content['data']
                                print(f"  📋 嵌套data类型: {type(data_list)}")
                                print(f"  📊 嵌套data长度: {len(data_list) if isinstance(data_list, list) else 'N/A'}")
                            else:
                                print(f"  ⚠️ 嵌套data中没有data键")
                        elif isinstance(data_content, list):
                            # 新格式：直接列表
                            data_list = data_content
                            print(f"  📊 直接列表长度: {len(data_list)}")
                        else:
                            print(f"  ⚠️ data内容格式异常: {type(data_content)}")
                            continue
                    else:
                        print(f"  ⚠️ 字典中没有data键")
                        continue
                elif isinstance(file_data, list):
                    data_list = file_data
                    print(f"  📊 直接列表长度: {len(data_list)}")
                else:
                    print(f"  ⚠️ 文件格式异常: {type(file_data)}")
                    continue
                
                # 3. 检查数据内容
                if data_list and len(data_list) > 0:
                    print(f"  ✅ 数据列表有效: {len(data_list)} 条")
                    
                    # 检查第一条数据
                    first_item = data_list[0]
                    print(f"  📋 第一条数据类型: {type(first_item)}")
                    
                    if isinstance(first_item, dict):
                        print(f"  🔑 第一条数据键: {list(first_item.keys())}")
                        
                        # 检查关键字段
                        if 'trade_date' in first_item:
                            print(f"  📅 trade_date: {first_item['trade_date']}")
                        if 'close' in first_item:
                            print(f"  💰 close: {first_item['close']}")
                    
                    # 4. 尝试转换为DataFrame
                    try:
                        df = pd.DataFrame(data_list)
                        print(f"  ✅ DataFrame转换成功: {df.shape}")
                        print(f"  📋 DataFrame列: {list(df.columns)}")
                        
                        # 5. 测试日期过滤
                        if 'trade_date' in df.columns:
                            print(f"  📅 原始日期范围: {df['trade_date'].min()} - {df['trade_date'].max()}")
                            
                            # 转换日期格式
                            df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d', errors='coerce')
                            start_dt = pd.to_datetime('20240101', format='%Y%m%d')
                            end_dt = pd.to_datetime('20250625', format='%Y%m%d')
                            
                            filtered_df = df[(df['trade_date'] >= start_dt) & (df['trade_date'] <= end_dt)]
                            print(f"  📊 过滤后数据: {len(filtered_df)} 条")
                            
                            if len(filtered_df) > 0:
                                print(f"  ✅ 数据加载应该成功")
                            else:
                                print(f"  ⚠️ 过滤后数据为空")
                        else:
                            print(f"  ⚠️ 没有trade_date列")
                    
                    except Exception as e:
                        print(f"  ❌ DataFrame转换失败: {str(e)}")
                else:
                    print(f"  ❌ 数据列表为空")
                
            except Exception as e:
                print(f"  ❌ 处理失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def extract_stock_code_from_filename(filename):
    """从文件名中提取股票代码（支持新旧两种格式）"""
    try:
        # 移除.json后缀
        name_without_ext = filename.replace('.json', '')
        
        # 新格式：直接是股票代码（如 000001.SZ）
        if not name_without_ext.startswith('stock_daily_'):
            stock_code = name_without_ext
        else:
            # 旧格式：stock_daily_{股票代码}_end_date_{日期}_start_date_{日期}
            # 例如：stock_daily_000001.SZ_end_date_20240112_start_date_20240101
            parts = name_without_ext.split('_')
            if len(parts) >= 3:
                stock_code = parts[2]  # 第3个部分是股票代码
            else:
                stock_code = name_without_ext
        
        # 确保股票代码格式正确
        if '.' not in stock_code:
            if stock_code.startswith('6'):
                stock_code += '.SH'
            elif stock_code.startswith(('0', '3')):
                stock_code += '.SZ'
            elif stock_code.startswith('8'):
                stock_code += '.BJ'  # 北交所
            elif stock_code.startswith('4'):
                stock_code += '.SZ'  # 新三板转板
        
        return stock_code
        
    except Exception as e:
        print(f"⚠️ 解析文件名失败 {filename}: {str(e)}")
        return None

def test_direct_loading():
    """测试直接加载方法"""
    print(f"\n🧪 测试直接加载方法")
    print("=" * 50)
    
    try:
        from 多股票回测系统 import MultiStockDataManager
        
        data_manager = MultiStockDataManager(None)
        
        # 测试加载第一个股票
        cached_stocks = data_manager.get_all_cached_stocks()
        
        if cached_stocks:
            test_stock = cached_stocks[0]
            print(f"📈 测试股票: {test_stock}")
            
            # 调用直接加载方法
            data = data_manager._load_stock_data_from_cache_file(test_stock, "20240101", "20250625")
            
            if data is not None and not data.empty:
                print(f"✅ 加载成功: {len(data)} 条数据")
                print(f"📋 列名: {list(data.columns)}")
                print(f"📊 数据样本:")
                print(data.head())
            else:
                print(f"❌ 加载失败: 数据为空")
        else:
            print(f"❌ 没有缓存股票可测试")
    
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始调试缓存文件加载问题")
    print("=" * 60)
    
    # 1. 调试缓存文件加载
    debug_cache_file_loading()
    
    # 2. 测试直接加载方法
    test_direct_loading()
    
    print("\n" + "=" * 60)
    print("📊 调试总结:")
    print("  通过以上调试信息，可以确定问题所在")

if __name__ == "__main__":
    main()
