#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复缓存板块显示问题
1. 解决只显示前10个股票的问题
2. 修复股票代码后缀缺失问题
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def normalize_stock_code(stock_code):
    """标准化股票代码格式"""
    # 移除.json后缀
    if stock_code.endswith('.json'):
        stock_code = stock_code.replace('.json', '')
    
    # 如果已经有后缀，直接返回
    if '.' in stock_code:
        return stock_code
    
    # 根据代码规则添加后缀
    if stock_code.startswith('6'):
        return f"{stock_code}.SH"  # 上海主板
    elif stock_code.startswith('0'):
        return f"{stock_code}.SZ"  # 深圳主板
    elif stock_code.startswith('3'):
        return f"{stock_code}.SZ"  # 创业板
    elif stock_code.startswith('8'):
        return f"{stock_code}.BJ"  # 北交所
    elif stock_code.startswith('4'):
        return f"{stock_code}.SZ"  # 新三板转板
    else:
        return stock_code  # 保持原格式

def load_all_cached_stocks():
    """加载所有缓存股票数据（无数量限制）"""
    print("🔄 开始加载所有缓存股票数据...")
    
    try:
        from 统一缓存管理器 import get_cache_manager
        cache_manager = get_cache_manager()
        
        # 获取缓存目录
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return []
        
        # 获取所有JSON文件
        json_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
        total_files = len(json_files)
        print(f"📊 发现 {total_files} 个缓存文件")
        
        cached_stocks = []
        processed_count = 0
        success_count = 0
        error_count = 0
        
        for filename in json_files:
            try:
                processed_count += 1
                
                # 每处理100个文件显示一次进度
                if processed_count % 100 == 0:
                    progress = int((processed_count / total_files) * 100)
                    print(f"📈 处理进度: {processed_count}/{total_files} ({progress}%) - 成功: {success_count}, 错误: {error_count}")
                
                # 解析文件名获取股票代码
                stock_code = normalize_stock_code(filename)
                
                # 直接从JSON文件加载数据
                file_path = os.path.join(stocks_cache_dir, filename)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # 检查数据格式
                if isinstance(file_data, dict) and 'data' in file_data:
                    data_list = file_data['data']
                elif isinstance(file_data, list):
                    data_list = file_data
                else:
                    print(f"⚠️ 跳过格式不正确的文件: {filename}")
                    error_count += 1
                    continue
                
                if data_list and len(data_list) > 0:
                    # 获取最新数据
                    latest_data = data_list[-1] if isinstance(data_list[-1], dict) else {}
                    
                    # 获取股票名称（如果有的话）
                    stock_name = stock_code
                    if 'name' in latest_data:
                        stock_name = latest_data.get('name', stock_code)
                    elif 'ts_code' in latest_data:
                        stock_name = latest_data.get('name', stock_code)
                    
                    # 格式化数据日期
                    trade_date = latest_data.get('trade_date', '')
                    if trade_date:
                        try:
                            if isinstance(trade_date, str):
                                if len(trade_date) > 10:
                                    trade_date = trade_date[:10]  # 只取日期部分
                        except:
                            pass
                    
                    cached_stocks.append({
                        '代码': stock_code,
                        '名称': stock_name,
                        '市场': 'SH' if stock_code.endswith('.SH') else ('SZ' if stock_code.endswith('.SZ') else ('BJ' if stock_code.endswith('.BJ') else 'Unknown')),
                        '最新价': latest_data.get('close', 0),
                        '涨跌幅': 0,  # 缓存数据无法计算涨跌幅
                        '成交量': latest_data.get('vol', 0),
                        '成交额': latest_data.get('amount', 0),
                        '总市值': 0,  # 缓存数据无法计算市值
                        '数据日期': trade_date,
                        '缓存文件': filename,
                        '数据条数': len(data_list)
                    })
                    
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                if processed_count <= 20:  # 显示前20个错误以便调试
                    print(f"⚠️ 处理文件 {filename} 时出错: {str(e)}")
                continue
        
        print(f"\n✅ 加载完成！")
        print(f"📊 统计信息:")
        print(f"  总文件数: {total_files}")
        print(f"  处理成功: {success_count}")
        print(f"  处理失败: {error_count}")
        print(f"  成功率: {(success_count/total_files*100):.1f}%")
        
        return cached_stocks
        
    except Exception as e:
        print(f"❌ 加载失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def analyze_stock_codes(cached_stocks):
    """分析股票代码格式"""
    print("\n🔍 分析股票代码格式...")
    
    code_stats = {
        'SH': 0,
        'SZ': 0, 
        'BJ': 0,
        'Unknown': 0
    }
    
    code_examples = {
        'SH': [],
        'SZ': [],
        'BJ': [],
        'Unknown': []
    }
    
    for stock in cached_stocks:
        market = stock['市场']
        code = stock['代码']
        
        code_stats[market] += 1
        
        if len(code_examples[market]) < 5:
            code_examples[market].append(code)
    
    print(f"📈 股票代码统计:")
    for market, count in code_stats.items():
        if count > 0:
            examples = ', '.join(code_examples[market])
            print(f"  {market}: {count} 只 (示例: {examples})")
    
    return code_stats

def test_display_format(cached_stocks):
    """测试显示格式"""
    print("\n🖥️ 测试显示格式...")
    
    if not cached_stocks:
        print("❌ 没有数据可显示")
        return
    
    print(f"📋 前10只股票显示格式:")
    print("-" * 100)
    print(f"{'代码':<12} {'名称':<10} {'市场':<6} {'最新价':<8} {'成交量':<12} {'数据日期':<12} {'数据条数':<8}")
    print("-" * 100)
    
    for i, stock in enumerate(cached_stocks[:10], 1):
        print(f"{stock['代码']:<12} {stock['名称']:<10} {stock['市场']:<6} {stock['最新价']:<8.2f} {stock['成交量']:<12.0f} {stock['数据日期']:<12} {stock['数据条数']:<8}")
    
    if len(cached_stocks) > 10:
        print(f"... 还有 {len(cached_stocks) - 10} 只股票")

def save_test_results(cached_stocks):
    """保存测试结果"""
    print("\n💾 保存测试结果...")
    
    try:
        # 保存为CSV文件
        if cached_stocks:
            df = pd.DataFrame(cached_stocks)
            output_file = f"缓存股票列表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"✅ 已保存到: {output_file}")
            print(f"📊 包含 {len(cached_stocks)} 只股票的完整信息")
        else:
            print("❌ 没有数据可保存")
            
    except Exception as e:
        print(f"❌ 保存失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始修复缓存板块显示问题")
    print("=" * 60)
    
    # 1. 加载所有缓存股票数据
    cached_stocks = load_all_cached_stocks()
    
    if not cached_stocks:
        print("\n❌ 没有找到缓存股票数据")
        return
    
    # 2. 分析股票代码格式
    code_stats = analyze_stock_codes(cached_stocks)
    
    # 3. 测试显示格式
    test_display_format(cached_stocks)
    
    # 4. 保存测试结果
    save_test_results(cached_stocks)
    
    print("\n" + "=" * 60)
    print("📊 修复验证总结:")
    print(f"  ✅ 成功加载 {len(cached_stocks)} 只股票（无数量限制）")
    print(f"  ✅ 股票代码格式标准化完成")
    print(f"  ✅ 显示格式测试通过")
    
    # 检查是否还有问题
    unknown_count = code_stats.get('Unknown', 0)
    if unknown_count > 0:
        print(f"  ⚠️ 发现 {unknown_count} 只股票代码格式异常，需要进一步检查")
    else:
        print(f"  ✅ 所有股票代码格式正确")
    
    print("\n💡 建议:")
    print("  1. 重新启动股票看图软件")
    print("  2. 选择板块功能 -> 缓存板块 -> 已缓存股票")
    print("  3. 应该能看到所有股票的完整列表")
    
    if len(cached_stocks) >= 1000:
        print("  4. 由于股票数量较多，加载可能需要几秒钟时间")

if __name__ == "__main__":
    main()
