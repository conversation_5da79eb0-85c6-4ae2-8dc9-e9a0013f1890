#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缓存板块股票显示功能
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cache_files_parsing():
    """测试缓存文件解析"""
    print("🧪 测试缓存文件解析")
    print("=" * 50)
    
    try:
        # 导入统一缓存管理器
        from 统一缓存管理器 import get_cache_manager
        cache_manager = get_cache_manager()
        
        # 获取缓存目录
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return False
        
        # 扫描缓存文件
        cache_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
        print(f"📊 发现 {len(cache_files)} 个缓存文件")
        
        # 测试解析前5个文件
        test_files = cache_files[:5]
        parsed_stocks = []
        
        for filename in test_files:
            try:
                print(f"\n🔍 解析文件: {filename}")
                
                # 解析股票代码
                stock_code = filename.replace('.json', '')
                
                # 确保股票代码格式正确
                if '.' not in stock_code:
                    if stock_code.startswith('6'):
                        stock_code += '.SH'
                    elif stock_code.startswith(('0', '3')):
                        stock_code += '.SZ'
                
                print(f"  📈 股票代码: {stock_code}")
                
                # 加载JSON数据
                file_path = os.path.join(stocks_cache_dir, filename)
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # 检查数据格式
                if isinstance(file_data, dict) and 'data' in file_data:
                    data_list = file_data['data']
                    print(f"  📋 数据格式: 包装格式 (data字段)")
                elif isinstance(file_data, list):
                    data_list = file_data
                    print(f"  📋 数据格式: 直接列表")
                else:
                    print(f"  ⚠️ 未知数据格式: {type(file_data)}")
                    continue
                
                if data_list and len(data_list) > 0:
                    # 获取最新数据
                    latest_data = data_list[-1] if isinstance(data_list[-1], dict) else {}
                    
                    print(f"  📊 数据条数: {len(data_list)}")
                    print(f"  📅 最新日期: {latest_data.get('trade_date', 'N/A')}")
                    print(f"  💰 最新价格: {latest_data.get('close', 'N/A')}")
                    print(f"  📈 成交量: {latest_data.get('vol', 'N/A')}")
                    
                    # 构造股票信息
                    stock_info = {
                        '代码': stock_code,
                        '名称': latest_data.get('name', stock_code),
                        '市场': 'SH' if stock_code.endswith('.SH') else 'SZ',
                        '最新价': latest_data.get('close', 0),
                        '涨跌幅': 0,
                        '成交量': latest_data.get('vol', 0),
                        '成交额': latest_data.get('amount', 0),
                        '总市值': 0,
                        '数据日期': latest_data.get('trade_date', ''),
                        '缓存文件': filename,
                        '数据条数': len(data_list)
                    }
                    
                    parsed_stocks.append(stock_info)
                    print(f"  ✅ 解析成功")
                else:
                    print(f"  ❌ 数据为空")
                    
            except Exception as e:
                print(f"  ❌ 解析失败: {str(e)}")
                continue
        
        print(f"\n📊 解析结果总结:")
        print(f"  总文件数: {len(cache_files)}")
        print(f"  测试文件数: {len(test_files)}")
        print(f"  成功解析: {len(parsed_stocks)}")
        
        if parsed_stocks:
            print(f"\n📋 解析成功的股票:")
            for stock in parsed_stocks:
                print(f"  {stock['代码']} - {stock['名称']} - 最新价: {stock['最新价']} - 数据条数: {stock['数据条数']}")
        
        return len(parsed_stocks) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_load_cached_stocks_simulation():
    """模拟加载缓存股票数据"""
    print("\n🔄 模拟加载缓存股票数据")
    print("=" * 50)
    
    try:
        from 统一缓存管理器 import get_cache_manager
        cache_manager = get_cache_manager()
        
        # 模拟 load_cached_stocks_data 方法的逻辑
        sector_name = "已缓存股票 (1026个文件)"
        cached_stocks = []
        
        if "已缓存股票" in sector_name:
            # 加载已缓存的股票数据
            stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
            if os.path.exists(stocks_cache_dir):
                print(f"📁 扫描缓存目录: {stocks_cache_dir}")
                
                file_count = 0
                success_count = 0
                
                for filename in os.listdir(stocks_cache_dir):
                    if filename.endswith('.json'):
                        file_count += 1
                        
                        # 只处理前10个文件进行测试
                        if success_count >= 10:
                            break
                            
                        try:
                            # 解析文件名获取股票代码
                            stock_code = filename.replace('.json', '')
                            
                            # 确保股票代码格式正确
                            if '.' not in stock_code:
                                if stock_code.startswith('6'):
                                    stock_code += '.SH'
                                elif stock_code.startswith(('0', '3')):
                                    stock_code += '.SZ'

                            # 直接从JSON文件加载数据
                            file_path = os.path.join(stocks_cache_dir, filename)
                            
                            with open(file_path, 'r', encoding='utf-8') as f:
                                file_data = json.load(f)
                            
                            # 检查数据格式
                            if isinstance(file_data, dict) and 'data' in file_data:
                                data_list = file_data['data']
                            elif isinstance(file_data, list):
                                data_list = file_data
                            else:
                                continue
                            
                            if data_list and len(data_list) > 0:
                                # 获取最新数据
                                latest_data = data_list[-1] if isinstance(data_list[-1], dict) else {}
                                
                                # 获取股票名称（如果有的话）
                                stock_name = stock_code
                                if 'name' in latest_data:
                                    stock_name = latest_data.get('name', stock_code)
                                
                                cached_stocks.append({
                                    '代码': stock_code,
                                    '名称': stock_name,
                                    '市场': 'SH' if stock_code.endswith('.SH') else 'SZ',
                                    '最新价': latest_data.get('close', 0),
                                    '涨跌幅': 0,
                                    '成交量': latest_data.get('vol', 0),
                                    '成交额': latest_data.get('amount', 0),
                                    '总市值': 0,
                                    '数据日期': latest_data.get('trade_date', ''),
                                    '缓存文件': filename,
                                    '数据条数': len(data_list)
                                })
                                
                                success_count += 1
                                
                        except Exception as e:
                            print(f"⚠️ 处理文件 {filename} 时出错: {str(e)}")
                            continue
                
                print(f"✅ 成功加载 {len(cached_stocks)} 只缓存股票 (总文件数: {file_count})")
        
        # 显示结果
        if cached_stocks:
            print(f"\n📋 加载的股票信息:")
            for i, stock in enumerate(cached_stocks[:5], 1):  # 只显示前5个
                print(f"  {i}. {stock['代码']} - {stock['名称']}")
                print(f"     最新价: {stock['最新价']}, 数据日期: {stock['数据日期']}")
                print(f"     数据条数: {stock['数据条数']}, 缓存文件: {stock['缓存文件']}")
            
            if len(cached_stocks) > 5:
                print(f"  ... 还有 {len(cached_stocks) - 5} 只股票")
        
        return len(cached_stocks) > 0
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试缓存板块股票显示功能")
    print("=" * 60)
    
    # 测试1: 缓存文件解析
    success1 = test_cache_files_parsing()
    
    # 测试2: 模拟加载缓存股票数据
    success2 = test_load_cached_stocks_simulation()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  缓存文件解析: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"  模拟数据加载: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！缓存板块股票显示功能应该正常工作。")
        print("\n💡 建议:")
        print("  1. 重新启动股票看图软件")
        print("  2. 选择板块功能 -> 缓存板块 -> 已缓存股票")
        print("  3. 应该能看到股票列表和详细信息")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
