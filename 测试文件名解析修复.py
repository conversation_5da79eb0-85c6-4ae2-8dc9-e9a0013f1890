#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件名解析修复
验证新旧两种格式的缓存文件都能正确解析
"""

import sys
import os
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def extract_stock_code_from_filename(filename):
    """从文件名中提取股票代码（支持新旧两种格式）"""
    try:
        # 移除.json后缀
        name_without_ext = filename.replace('.json', '')
        
        # 新格式：直接是股票代码（如 000001.SZ）
        if not name_without_ext.startswith('stock_daily_'):
            stock_code = name_without_ext
        else:
            # 旧格式：stock_daily_{股票代码}_end_date_{日期}_start_date_{日期}
            # 例如：stock_daily_000001.SZ_end_date_20240112_start_date_20240101
            parts = name_without_ext.split('_')
            if len(parts) >= 3:
                stock_code = parts[2]  # 第3个部分是股票代码
            else:
                stock_code = name_without_ext
        
        # 确保股票代码格式正确
        if '.' not in stock_code:
            if stock_code.startswith('6'):
                stock_code += '.SH'
            elif stock_code.startswith(('0', '3')):
                stock_code += '.SZ'
            elif stock_code.startswith('8'):
                stock_code += '.BJ'  # 北交所
            elif stock_code.startswith('4'):
                stock_code += '.SZ'  # 新三板转板
        
        return stock_code
        
    except Exception as e:
        print(f"⚠️ 解析文件名失败 {filename}: {str(e)}")
        return filename.replace('.json', '')

def test_filename_parsing():
    """测试文件名解析功能"""
    print("🧪 测试文件名解析功能")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # 新格式
        "000001.SZ.json",
        "600698.SH.json", 
        "300957.SZ.json",
        "831305.BJ.json",
        
        # 旧格式
        "stock_daily_000001.SZ_end_date_20240112_start_date_20240101.json",
        "stock_daily_600698.SH_end_date_20250625_start_date_20230626.json",
        "stock_daily_300957.SZ_end_date_20240120_start_date_20240101.json",
        
        # 无后缀格式
        "000002.json",
        "600000.json",
        "300001.json",
        "831001.json",
        
        # 旧格式无后缀
        "stock_daily_000002_end_date_20240112_start_date_20240101.json",
        "stock_daily_600000_end_date_20240112_start_date_20240101.json",
    ]
    
    print("📋 测试结果:")
    print("-" * 80)
    print(f"{'原文件名':<60} {'解析结果':<15}")
    print("-" * 80)
    
    success_count = 0
    for filename in test_cases:
        try:
            result = extract_stock_code_from_filename(filename)
            print(f"{filename:<60} {result:<15}")
            success_count += 1
        except Exception as e:
            print(f"{filename:<60} 错误: {str(e)}")
    
    print("-" * 80)
    print(f"✅ 测试完成: {success_count}/{len(test_cases)} 个用例通过")
    
    return success_count == len(test_cases)

def load_all_cached_stocks_with_new_parser():
    """使用新的解析器加载所有缓存股票"""
    print("\n🔄 使用新解析器加载所有缓存股票")
    print("=" * 50)
    
    try:
        from 统一缓存管理器 import get_cache_manager
        cache_manager = get_cache_manager()
        
        # 获取缓存目录
        stocks_cache_dir = cache_manager.cache_dirs.get('stocks', 'cache/stocks')
        print(f"📁 缓存目录: {stocks_cache_dir}")
        
        if not os.path.exists(stocks_cache_dir):
            print("❌ 缓存目录不存在")
            return []
        
        # 获取所有JSON文件
        json_files = [f for f in os.listdir(stocks_cache_dir) if f.endswith('.json')]
        total_files = len(json_files)
        print(f"📊 发现 {total_files} 个缓存文件")
        
        cached_stocks = []
        processed_count = 0
        success_count = 0
        error_count = 0
        
        # 分类统计
        new_format_count = 0
        old_format_count = 0
        
        for filename in json_files:
            try:
                processed_count += 1
                
                # 每处理100个文件显示一次进度
                if processed_count % 100 == 0:
                    progress = int((processed_count / total_files) * 100)
                    print(f"📈 处理进度: {processed_count}/{total_files} ({progress}%) - 成功: {success_count}, 错误: {error_count}")
                
                # 统计文件格式
                if filename.startswith('stock_daily_'):
                    old_format_count += 1
                else:
                    new_format_count += 1
                
                # 解析文件名获取股票代码
                stock_code = extract_stock_code_from_filename(filename)
                
                # 直接从JSON文件加载数据
                file_path = os.path.join(stocks_cache_dir, filename)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    file_data = json.load(f)
                
                # 检查数据格式（支持新旧两种JSON结构）
                if isinstance(file_data, dict) and 'data' in file_data:
                    # 新格式：{"data": [...]} 或 旧格式：{"data": {"type": "dataframe", "data": [...]}}
                    if isinstance(file_data['data'], dict) and 'data' in file_data['data']:
                        # 旧格式：嵌套结构
                        data_list = file_data['data']['data']
                    else:
                        # 新格式：直接列表
                        data_list = file_data['data']
                elif isinstance(file_data, list):
                    # 直接是列表格式
                    data_list = file_data
                else:
                    error_count += 1
                    continue
                
                if data_list and len(data_list) > 0:
                    # 获取最新数据
                    latest_data = data_list[-1] if isinstance(data_list[-1], dict) else {}
                    
                    # 获取股票名称
                    stock_name = latest_data.get('name', stock_code)
                    
                    # 格式化数据日期
                    trade_date = latest_data.get('trade_date', '')
                    if trade_date:
                        try:
                            if isinstance(trade_date, str) and len(trade_date) > 10:
                                trade_date = trade_date[:10]
                        except:
                            pass
                    
                    cached_stocks.append({
                        '代码': stock_code,
                        '名称': stock_name,
                        '市场': 'SH' if stock_code.endswith('.SH') else ('SZ' if stock_code.endswith('.SZ') else ('BJ' if stock_code.endswith('.BJ') else 'Unknown')),
                        '最新价': latest_data.get('close', 0),
                        '涨跌幅': 0,
                        '成交量': latest_data.get('vol', 0),
                        '成交额': latest_data.get('amount', 0),
                        '总市值': 0,
                        '数据日期': trade_date,
                        '缓存文件': filename,
                        '数据条数': len(data_list),
                        '文件格式': '新格式' if not filename.startswith('stock_daily_') else '旧格式'
                    })
                    
                    success_count += 1
                else:
                    error_count += 1
                    
            except Exception as e:
                error_count += 1
                if error_count <= 10:  # 只显示前10个错误
                    print(f"⚠️ 处理文件 {filename} 时出错: {str(e)}")
                continue
        
        print(f"\n✅ 加载完成！")
        print(f"📊 统计信息:")
        print(f"  总文件数: {total_files}")
        print(f"  新格式文件: {new_format_count}")
        print(f"  旧格式文件: {old_format_count}")
        print(f"  处理成功: {success_count}")
        print(f"  处理失败: {error_count}")
        print(f"  成功率: {(success_count/total_files*100):.1f}%")
        
        return cached_stocks
        
    except Exception as e:
        print(f"❌ 加载失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def analyze_results(cached_stocks):
    """分析加载结果"""
    print(f"\n📊 结果分析")
    print("=" * 50)
    
    if not cached_stocks:
        print("❌ 没有数据可分析")
        return
    
    # 按市场分类
    market_stats = {}
    format_stats = {}
    
    for stock in cached_stocks:
        market = stock['市场']
        file_format = stock['文件格式']
        
        market_stats[market] = market_stats.get(market, 0) + 1
        format_stats[file_format] = format_stats.get(file_format, 0) + 1
    
    print(f"📈 按市场分类:")
    for market, count in market_stats.items():
        print(f"  {market}: {count} 只")
    
    print(f"\n📁 按文件格式分类:")
    for fmt, count in format_stats.items():
        print(f"  {fmt}: {count} 个文件")
    
    # 显示前10只股票
    print(f"\n📋 前10只股票:")
    print("-" * 100)
    print(f"{'代码':<12} {'名称':<10} {'市场':<6} {'最新价':<8} {'数据日期':<12} {'文件格式':<8}")
    print("-" * 100)
    
    for i, stock in enumerate(cached_stocks[:10], 1):
        print(f"{stock['代码']:<12} {stock['名称']:<10} {stock['市场']:<6} {stock['最新价']:<8.2f} {stock['数据日期']:<12} {stock['文件格式']:<8}")

def main():
    """主函数"""
    print("🚀 开始测试文件名解析修复")
    print("=" * 60)
    
    # 1. 测试文件名解析功能
    parsing_success = test_filename_parsing()
    
    if not parsing_success:
        print("\n❌ 文件名解析测试失败，请检查代码")
        return
    
    # 2. 使用新解析器加载所有缓存股票
    cached_stocks = load_all_cached_stocks_with_new_parser()
    
    # 3. 分析结果
    analyze_results(cached_stocks)
    
    print("\n" + "=" * 60)
    print("📊 修复验证总结:")
    
    if cached_stocks:
        success_rate = len(cached_stocks) / 1027 * 100  # 假设总共1027个文件
        print(f"  ✅ 成功加载 {len(cached_stocks)} 只股票")
        print(f"  ✅ 成功率: {success_rate:.1f}%")
        
        if success_rate > 90:
            print(f"  🎉 修复效果优秀！大部分文件都能正确解析")
        elif success_rate > 70:
            print(f"  ✨ 修复效果良好！大多数文件都能正确解析")
        else:
            print(f"  ⚠️ 修复效果一般，仍有部分文件无法解析")
    else:
        print(f"  ❌ 修复失败，没有成功加载任何股票")
    
    print("\n💡 建议:")
    print("  1. 重新启动股票看图软件")
    print("  2. 选择板块功能 -> 缓存板块 -> 已缓存股票")
    print("  3. 现在应该能看到更多股票的完整列表")

if __name__ == "__main__":
    main()
